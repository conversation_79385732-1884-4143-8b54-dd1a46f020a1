#include <stdio.h>
#include <stdlib.h>
#include <string.h>

#define MAX_LINE_LENGTH 1024

// Function to resolve relative paths
void resolve_relative_path(const char* base_dir, const char* relative_path, char* resolved_path) {
    char temp_base[MAX_LINE_LENGTH];
    strcpy(temp_base, base_dir);

    const char* path_ptr = relative_path;

    // Handle relative path components
    while (strncmp(path_ptr, "../", 3) == 0) {
        // Go up one directory
        char* last_slash = strrchr(temp_base, '/');
        if (last_slash) {
            *last_slash = '\0';
        } else {
            strcpy(temp_base, ".");
        }
        path_ptr += 3; // Skip "../"
    }

    // Handle current directory reference
    if (strncmp(path_ptr, "./", 2) == 0) {
        path_ptr += 2; // Skip "./"
    }

    // Combine base directory with remaining path
    if (strcmp(temp_base, ".") == 0) {
        strcpy(resolved_path, path_ptr);
    } else {
        snprintf(resolved_path, MAX_LINE_LENGTH, "%s/%s", temp_base, path_ptr);
    }
}

// Function to find the index of a file in the files array
int find_file_index(char files[][MAX_LINE_LENGTH], int file_count, const char* filename) {
    for (int i = 0; i < file_count; i++) {
        if (strcmp(files[i], filename) == 0) {
            return i;
        }
    }
    return -1;
}

// Function to insert dependencies right before the requiring file
void insert_dependencies_before_file(char files[][MAX_LINE_LENGTH], int* file_count, int file_index) {
    char file_dependencies[100][MAX_LINE_LENGTH];
    int dep_count = 0;

    // Get the directory of the current file for relative path resolution
    char file_dir[MAX_LINE_LENGTH];
    strcpy(file_dir, files[file_index]);
    char* last_slash = strrchr(file_dir, '/');
    if (last_slash) {
        *last_slash = '\0';
    } else {
        strcpy(file_dir, ".");
    }

    // Scan this file for requirements
    FILE* ts_file = fopen(files[file_index], "r");
    if (!ts_file) return;

    char file_line[MAX_LINE_LENGTH];
    while (fgets(file_line, sizeof(file_line), ts_file)) {
        file_line[strcspn(file_line, "\n")] = 0;

        if (strncmp(file_line, "//@tsBind-require", 17) == 0) {
            char* required_file = file_line + 18;
            while (*required_file == ' ') required_file++;

            char resolved_path[MAX_LINE_LENGTH];
            if (required_file[0] == '.' || strstr(required_file, "../") != NULL) {
                resolve_relative_path(file_dir, required_file, resolved_path);
            } else {
                strcpy(resolved_path, required_file);
            }

            // Check if this dependency is already before this file in the list
            int already_before = 0;
            for (int i = 0; i < file_index; i++) {
                if (strcmp(files[i], resolved_path) == 0) {
                    already_before = 1;
                    break;
                }
            }

            // If not already before this file, add it to dependencies list
            if (!already_before) {
                strcpy(file_dependencies[dep_count++], resolved_path);
            }
        }
    }
    fclose(ts_file);

    // If we have dependencies to insert, shift files and insert them
    if (dep_count > 0) {
        // Shift files to make room for dependencies
        for (int i = *file_count - 1; i >= file_index; i--) {
            strcpy(files[i + dep_count], files[i]);
        }

        // Insert dependencies
        for (int i = 0; i < dep_count; i++) {
            strcpy(files[file_index + i], file_dependencies[i]);
            printf("Inserting dependency before %s: %s\n", files[file_index + dep_count], file_dependencies[i]);
        }

        *file_count += dep_count;
    }
}

// Function to remove duplicate files, keeping only the first occurrence
void remove_duplicate_files(char files[][MAX_LINE_LENGTH], int* file_count) {
    int write_index = 0;

    for (int read_index = 0; read_index < *file_count; read_index++) {
        // Check if this file already exists earlier in the array
        int is_duplicate = 0;
        for (int check_index = 0; check_index < write_index; check_index++) {
            if (strcmp(files[read_index], files[check_index]) == 0) {
                is_duplicate = 1;
                printf("Removing duplicate: %s (keeping earlier instance)\n", files[read_index]);
                break;
            }
        }

        // If not a duplicate, keep it
        if (!is_duplicate) {
            if (write_index != read_index) {
                strcpy(files[write_index], files[read_index]);
            }
            write_index++;
        }
    }

    *file_count = write_index;
}

// Function to process dependencies for all files
void process_file_dependencies(char files[][MAX_LINE_LENGTH], int* file_count) {
    // Process files from end to beginning to avoid index shifting issues
    for (int f = *file_count - 1; f >= 0; f--) {
        insert_dependencies_before_file(files, file_count, f);
    }

    // Remove any duplicate files that may have been created
    remove_duplicate_files(files, file_count);

    printf("Final file processing order:\n");
    for (int i = 0; i < *file_count; i++) {
        printf("  %d: %s\n", i + 1, files[i]);
    }
}

int main() {
    
    // Read .tsBind file
    FILE* config_file = fopen(".tsBind", "r");
    if (!config_file) {
        printf("Error: Cannot open .tsBind file\n");
        return 1;
    }
    
    char output_file[MAX_LINE_LENGTH];
    char line[MAX_LINE_LENGTH];
    
    // Read first line (output file)
    if (!fgets(output_file, sizeof(output_file), config_file)) {
        printf("Error: Cannot read output file from .tsBind\n");
        fclose(config_file);
        return 1;
    }
    output_file[strcspn(output_file, "\n")] = 0; // Remove newline
    
    // Open output file for writing
    FILE* out = fopen(output_file, "w");
    if (!out) {
        printf("Error: Cannot create output file %s\n", output_file);
        fclose(config_file);
        return 1;
    }
    
    // Write header
    fprintf(out, "//@tsBind-Generated\n\n");

    // First pass: collect imports
    char imports[100][MAX_LINE_LENGTH];
    char files[100][MAX_LINE_LENGTH];
    char requirements[100][MAX_LINE_LENGTH];
    int import_count = 0;
    int file_count = 0;
    int requirement_count = 0;

    while (fgets(line, sizeof(line), config_file)) {
        line[strcspn(line, "\n")] = 0; // Remove newline

        if (strlen(line) == 0) continue;

        if (strncmp(line, "import", 6) == 0 && strstr(line, "=") == NULL) {
            strcpy(imports[import_count++], line);
        }
        else if (strncmp(line, "//@tsBind-require", 17) == 0) {
            // Extract the required file path (skip "//@tsBind-require ")
            char* required_file = line + 18; // Skip "//@tsBind-require "
            while (*required_file == ' ') required_file++; // Skip any extra spaces
            strcpy(requirements[requirement_count++], required_file);
        }
        else
        {
            strcpy(files[file_count++], line);
        }
    }

    // Second pass: scan TypeScript files for additional requirements
    for (int f = 0; f < file_count; f++) {
        FILE* ts_file = fopen(files[f], "r");
        if (!ts_file) {
            printf("Warning: Cannot open file %s for requirement scanning\n", files[f]);
            continue;
        }

        // Get the directory of the current file for relative path resolution
        char file_dir[MAX_LINE_LENGTH];
        strcpy(file_dir, files[f]);
        char* last_slash = strrchr(file_dir, '/');
        if (last_slash) {
            *last_slash = '\0'; // Keep only the directory part
        } else {
            strcpy(file_dir, "."); // Current directory if no slash found
        }

        char file_line[MAX_LINE_LENGTH];
        while (fgets(file_line, sizeof(file_line), ts_file)) {
            file_line[strcspn(file_line, "\n")] = 0; // Remove newline

            // Check for requirements in TypeScript files
            if (strncmp(file_line, "//@tsBind-require", 17) == 0) {
                char* required_file = file_line + 18; // Skip "//@tsBind-require "
                while (*required_file == ' ') required_file++; // Skip any extra spaces

                // Resolve relative path
                char resolved_path[MAX_LINE_LENGTH];
                if (required_file[0] == '.' || strstr(required_file, "../") != NULL) {
                    // This is a relative path, resolve it
                    resolve_relative_path(file_dir, required_file, resolved_path);
                } else {
                    // Absolute or already resolved path
                    strcpy(resolved_path, required_file);
                }

                // Check if this requirement is already in our list
                int already_exists = 0;
                for (int r = 0; r < requirement_count; r++) {
                    if (strcmp(requirements[r], resolved_path) == 0) {
                        already_exists = 1;
                        break;
                    }
                }

                if (!already_exists) {
                    strcpy(requirements[requirement_count++], resolved_path);
                    printf("Found requirement in %s: %s -> %s\n", files[f], required_file, resolved_path);
                }
            }
        }
        fclose(ts_file);
    }

    // Process dependencies - insert required files before requiring files
    process_file_dependencies(files, &file_count);

    // Validate requirements - check that all required files exist
    for (int i = 0; i < requirement_count; i++) {
        FILE* req_file = fopen(requirements[i], "r");
        if (!req_file) {
            printf("Error: Required file '%s' not found\n", requirements[i]);
            fclose(config_file);
            fclose(out);
            return 1;
        }
        fclose(req_file);
        printf("Requirement satisfied: %s\n", requirements[i]);
    }

    // Write all imports first
    for (int i = 0; i < import_count; i++) {
        fprintf(out, "%s\n", imports[i]);
    }
    fprintf(out, "\n");

    // Process global requirements from .tsBind file first
    for (int r = 0; r < requirement_count; r++) {
        printf("Processing global requirement: %s\n", requirements[r]);

        FILE* req_file = fopen(requirements[r], "r");
        if (!req_file) {
            printf("Warning: Cannot open global requirement file %s\n", requirements[r]);
            continue;
        }

        // Create namespace name for the requirement
        char namespace_name[MAX_LINE_LENGTH];
        strcpy(namespace_name, requirements[r]);

        // Remove extension
        char* dot = strrchr(namespace_name, '.');
        if (dot) *dot = '\0';

        // Remove src/ prefix
        if (strncmp(namespace_name, "src/", 4) == 0) {
            memmove(namespace_name, namespace_name + 4, strlen(namespace_name) - 3);
        }

        // Replace / with .
        for (int i = 0; namespace_name[i]; i++) {
            if (namespace_name[i] == '/') {
                namespace_name[i] = '.';
            }
        }

        fprintf(out, "\n// ===== %s (Global Requirement) =====\n\n", requirements[r]);
        fprintf(out, "namespace %s {\n", namespace_name);

        // Copy file content, skipping import lines
        char file_line[MAX_LINE_LENGTH];
        while (fgets(file_line, sizeof(file_line), req_file)) {
            // Skip import lines and requirement lines
            char* trimmed = file_line;
            while (*trimmed == ' ' || *trimmed == '\t') trimmed++;

            if (!(strncmp(trimmed, "import", 6) == 0 && strstr(trimmed, "=") == NULL) &&
                !(strncmp(trimmed, "const", 5) == 0 && strstr(trimmed, "require(") != NULL) &&
                !(strncmp(trimmed, "//@tsBind-require", 17) == 0)) {

                // Replace $ with . and add indentation
                fprintf(out, "    ");
                for (int i = 0; file_line[i]; i++) {
                    if (file_line[i] == '$') {
                        fputc('.', out);
                    } else {
                        fputc(file_line[i], out);
                    }
                }
            }
        }

        fprintf(out, "}\n");
        fclose(req_file);
    }

    // Process TypeScript files
    for (int f = 0; f < file_count; f++) {
        char* line = files[f];
            // This is a TypeScript file - process it
            printf("Processing file: %s\n", line);
            
            FILE* ts_file = fopen(line, "r");
            if (!ts_file) {
                printf("Warning: Cannot open file %s\n", line);
                continue;
            }
            
            // Create namespace name
            char namespace_name[MAX_LINE_LENGTH];
            strcpy(namespace_name, line);
            
            // Remove extension
            char* dot = strrchr(namespace_name, '.');
            if (dot) *dot = '\0';
            
            // Remove src/ prefix
            if (strncmp(namespace_name, "src/", 4) == 0) {
                memmove(namespace_name, namespace_name + 4, strlen(namespace_name) - 3);
            }
            
            // Replace / with .
            for (int i = 0; namespace_name[i]; i++) {
                if (namespace_name[i] == '/') {
                    namespace_name[i] = '.';
                }
            }
            
            fprintf(out, "\n// ===== %s =====\n\n", line);
            fprintf(out, "namespace %s {\n", namespace_name);
            
            // Copy file content, skipping import lines
            char file_line[MAX_LINE_LENGTH];
            while (fgets(file_line, sizeof(file_line), ts_file)) {
                // Skip import lines
                char* trimmed = file_line;
                while (*trimmed == ' ' || *trimmed == '\t') trimmed++;
                
                if (!(strncmp(trimmed, "import", 6) == 0 && strstr(trimmed, "=") == NULL) &&
                    !(strncmp(trimmed, "const", 5) == 0 && strstr(trimmed, "require(") != NULL)) {
                    
                    // Replace $ with . and add indentation
                    fprintf(out, "    ");
                    for (int i = 0; file_line[i]; i++) {
                        if (file_line[i] == '$') {
                            fputc('.', out);
                        } else {
                            fputc(file_line[i], out);
                        }
                    }
                }
            }
            
            fprintf(out, "}\n");
            fclose(ts_file);
    }

    fclose(config_file);
    fclose(out);
    
    printf("Combined TypeScript file written to %s\n", output_file);
    return 0;
}
