#include <stdio.h>
#include <stdlib.h>
#include <string.h>

#define MAX_LINE_LENGTH 1024

int main() {
    
    // Read .tsBind file
    FILE* config_file = fopen(".tsBind", "r");
    if (!config_file) {
        printf("Error: Cannot open .tsBind file\n");
        return 1;
    }
    
    char output_file[MAX_LINE_LENGTH];
    char line[MAX_LINE_LENGTH];
    
    // Read first line (output file)
    if (!fgets(output_file, sizeof(output_file), config_file)) {
        printf("Error: Cannot read output file from .tsBind\n");
        fclose(config_file);
        return 1;
    }
    output_file[strcspn(output_file, "\n")] = 0; // Remove newline
    
    // Open output file for writing
    FILE* out = fopen(output_file, "w");
    if (!out) {
        printf("Error: Cannot create output file %s\n", output_file);
        fclose(config_file);
        return 1;
    }
    
    // Write header
    fprintf(out, "//@tsBind-Generated\n\n");

    // First pass: collect imports
    char imports[100][MAX_LINE_LENGTH];
    char files[100][MAX_LINE_LENGTH];
    char requirements[100][MAX_LINE_LENGTH];
    int import_count = 0;
    int file_count = 0;
    int requirement_count = 0;

    while (fgets(line, sizeof(line), config_file)) {
        line[strcspn(line, "\n")] = 0; // Remove newline

        if (strlen(line) == 0) continue;

        if (strncmp(line, "import", 6) == 0 && strstr(line, "=") == NULL) {
            strcpy(imports[import_count++], line);
        }
        else if (strncmp(line, "//@tsBind-require", 17) == 0) {
            // Extract the required file path (skip "//@tsBind-require ")
            char* required_file = line + 18; // Skip "//@tsBind-require "
            while (*required_file == ' ') required_file++; // Skip any extra spaces
            strcpy(requirements[requirement_count++], required_file);
        }
        else
        {
            strcpy(files[file_count++], line);
        }
    }

    // Second pass: scan TypeScript files for additional requirements
    for (int f = 0; f < file_count; f++) {
        FILE* ts_file = fopen(files[f], "r");
        if (!ts_file) {
            printf("Warning: Cannot open file %s for requirement scanning\n", files[f]);
            continue;
        }

        char file_line[MAX_LINE_LENGTH];
        while (fgets(file_line, sizeof(file_line), ts_file)) {
            file_line[strcspn(file_line, "\n")] = 0; // Remove newline

            // Check for requirements in TypeScript files
            if (strncmp(file_line, "//@tsBind-require", 17) == 0) {
                char* required_file = file_line + 18; // Skip "//@tsBind-require "
                while (*required_file == ' ') required_file++; // Skip any extra spaces

                // Check if this requirement is already in our list
                int already_exists = 0;
                for (int r = 0; r < requirement_count; r++) {
                    if (strcmp(requirements[r], required_file) == 0) {
                        already_exists = 1;
                        break;
                    }
                }

                if (!already_exists) {
                    strcpy(requirements[requirement_count++], required_file);
                    printf("Found requirement in %s: %s\n", files[f], required_file);
                }
            }
        }
        fclose(ts_file);
    }

    // Validate requirements - check that all required files exist
    for (int i = 0; i < requirement_count; i++) {
        FILE* req_file = fopen(requirements[i], "r");
        if (!req_file) {
            printf("Error: Required file '%s' not found\n", requirements[i]);
            fclose(config_file);
            fclose(out);
            return 1;
        }
        fclose(req_file);
        printf("Requirement satisfied: %s\n", requirements[i]);
    }

    // Write all imports first
    for (int i = 0; i < import_count; i++) {
        fprintf(out, "%s\n", imports[i]);
    }
    fprintf(out, "\n");

    // Process TypeScript files
    for (int f = 0; f < file_count; f++) {
        char* line = files[f];
            // This is a TypeScript file - process it
            printf("Processing file: %s\n", line);
            
            FILE* ts_file = fopen(line, "r");
            if (!ts_file) {
                printf("Warning: Cannot open file %s\n", line);
                continue;
            }
            
            // Create namespace name
            char namespace_name[MAX_LINE_LENGTH];
            strcpy(namespace_name, line);
            
            // Remove extension
            char* dot = strrchr(namespace_name, '.');
            if (dot) *dot = '\0';
            
            // Remove src/ prefix
            if (strncmp(namespace_name, "src/", 4) == 0) {
                memmove(namespace_name, namespace_name + 4, strlen(namespace_name) - 3);
            }
            
            // Replace / with .
            for (int i = 0; namespace_name[i]; i++) {
                if (namespace_name[i] == '/') {
                    namespace_name[i] = '.';
                }
            }
            
            fprintf(out, "\n// ===== %s =====\n\n", line);
            fprintf(out, "namespace %s {\n", namespace_name);
            
            // Copy file content, skipping import lines
            char file_line[MAX_LINE_LENGTH];
            while (fgets(file_line, sizeof(file_line), ts_file)) {
                // Skip import lines
                char* trimmed = file_line;
                while (*trimmed == ' ' || *trimmed == '\t') trimmed++;
                
                if (!(strncmp(trimmed, "import", 6) == 0 && strstr(trimmed, "=") == NULL) &&
                    !(strncmp(trimmed, "const", 5) == 0 && strstr(trimmed, "require(") != NULL)) {
                    
                    // Replace $ with . and add indentation
                    fprintf(out, "    ");
                    for (int i = 0; file_line[i]; i++) {
                        if (file_line[i] == '$') {
                            fputc('.', out);
                        } else {
                            fputc(file_line[i], out);
                        }
                    }
                }
            }
            
            fprintf(out, "}\n");
            fclose(ts_file);
    }

    fclose(config_file);
    fclose(out);
    
    printf("Combined TypeScript file written to %s\n", output_file);
    return 0;
}
